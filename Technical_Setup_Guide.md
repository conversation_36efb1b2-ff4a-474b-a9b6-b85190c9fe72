# TECHNICAL SETUP GUIDE
## React Frontend + <PERSON><PERSON> Backend

---

## 🚀 QUICK START COMMANDS

### **Prerequisites Installation**
```bash
# Install Node.js (v18+)
# Download from: https://nodejs.org/

# Install PHP (v8.2+)
# Download from: https://www.php.net/downloads

# Install Composer
# Download from: https://getcomposer.org/

# Install MySQL (v8.0+)
# Download from: https://dev.mysql.com/downloads/
```

### **Project Initialization**
```bash
# Create project directory
mkdir project-management-platform
cd project-management-platform

# Backend Setup (Laravel)
composer create-project laravel/laravel backend
cd backend
composer require laravel/sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

# Frontend Setup (React)
cd ..
npm create vite@latest frontend -- --template react-ts
cd frontend
npm install
```

---

## 🔧 BACKEND CONFIGURATION (<PERSON>vel)

### **Environment Setup (.env)**
```env
APP_NAME="Project Management Platform"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=project_management
DB_USERNAME=root
DB_PASSWORD=your-password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

SANCTUM_STATEFUL_DOMAINS=localhost:3000
```

### **Required Laravel Packages**
```bash
# Authentication & API
composer require laravel/sanctum

# File handling
composer require intervention/image

# CORS handling
composer require fruitcake/laravel-cors

# API resources
composer require spatie/laravel-query-builder

# Role & permissions
composer require spatie/laravel-permission

# Testing
composer require --dev pestphp/pest
composer require --dev pestphp/pest-plugin-laravel
```

### **Database Migrations**
```php
// Create migrations
php artisan make:migration create_roles_table
php artisan make:migration create_projects_table
php artisan make:migration create_project_users_table
php artisan make:migration create_plans_table
php artisan make:migration create_overlays_table
php artisan make:migration create_fire_penetrations_table
php artisan make:migration create_defects_table

// Run migrations
php artisan migrate
```

### **Laravel Models & Controllers**
```bash
# Generate models with controllers
php artisan make:model User -mcr
php artisan make:model Role -mcr
php artisan make:model Project -mcr
php artisan make:model Plan -mcr
php artisan make:model Overlay -mcr
php artisan make:model FirePenetration -mcr
php artisan make:model Defect -mcr

# Generate API resources
php artisan make:resource UserResource
php artisan make:resource ProjectResource
php artisan make:resource PlanResource
```

---

## ⚛️ FRONTEND CONFIGURATION (React)

### **Required React Packages**
```bash
# Core dependencies
npm install react-router-dom
npm install @tanstack/react-query
npm install axios
npm install zustand

# UI Components
npm install @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material
npm install @mui/x-data-grid

# Forms & Validation
npm install react-hook-form
npm install @hookform/resolvers
npm install zod

# Utilities
npm install date-fns
npm install lodash
npm install @types/lodash

# Development
npm install --save-dev @types/node
npm install --save-dev eslint-plugin-react-hooks
npm install --save-dev @typescript-eslint/eslint-plugin
```

### **Vite Configuration (vite.config.ts)**
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
})
```

### **TypeScript Configuration (tsconfig.json)**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

---

## 🗄️ DATABASE SCHEMA

### **Core Tables Structure**
```sql
-- Users table
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    role_id BIGINT UNSIGNED,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Roles table
CREATE TABLE roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    permissions JSON,
    level INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Projects table
CREATE TABLE projects (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status ENUM('planning', 'active', 'on_hold', 'completed', 'cancelled') DEFAULT 'planning',
    start_date DATE,
    end_date DATE,
    created_by BIGINT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Project Users (Many-to-Many)
CREATE TABLE project_users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT UNSIGNED,
    user_id BIGINT UNSIGNED,
    role VARCHAR(255) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_user (project_id, user_id)
);
```

---

## 🔐 AUTHENTICATION SETUP

### **Laravel Sanctum Configuration**
```php
// config/sanctum.php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
    '%s%s',
    'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
    Sanctum::currentApplicationUrlWithPort()
))),

'middleware' => [
    'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
    'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
],
```

### **API Routes (routes/api.php)**
```php
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\UserController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    
    // Projects
    Route::apiResource('projects', ProjectController::class);
    
    // Users (Admin only)
    Route::middleware('role:super_admin,project_admin')->group(function () {
        Route::apiResource('users', UserController::class);
    });
});
```

---

## 🎨 FRONTEND STRUCTURE

### **React App Structure**
```
src/
├── components/
│   ├── common/
│   │   ├── Layout.tsx
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── LoadingSpinner.tsx
│   ├── forms/
│   │   ├── LoginForm.tsx
│   │   ├── ProjectForm.tsx
│   │   └── UserForm.tsx
│   └── ui/
│       ├── Button.tsx
│       ├── Input.tsx
│       └── Modal.tsx
├── pages/
│   ├── auth/
│   │   ├── Login.tsx
│   │   └── Register.tsx
│   ├── dashboard/
│   │   └── Dashboard.tsx
│   ├── projects/
│   │   ├── ProjectList.tsx
│   │   ├── ProjectDetail.tsx
│   │   └── CreateProject.tsx
│   └── admin/
│       ├── UserManagement.tsx
│       └── RoleManagement.tsx
├── hooks/
│   ├── useAuth.ts
│   ├── useProjects.ts
│   └── useUsers.ts
├── services/
│   ├── api.ts
│   ├── auth.service.ts
│   └── project.service.ts
├── store/
│   ├── authStore.ts
│   ├── projectStore.ts
│   └── index.ts
├── types/
│   ├── auth.types.ts
│   ├── project.types.ts
│   └── user.types.ts
└── utils/
    ├── constants.ts
    ├── helpers.ts
    └── validators.ts
```

---

## 🚀 DEVELOPMENT COMMANDS

### **Backend Commands**
```bash
# Start Laravel development server
php artisan serve

# Run migrations
php artisan migrate

# Seed database
php artisan db:seed

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear

# Run tests
php artisan test
```

### **Frontend Commands**
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm run test

# Lint code
npm run lint

# Type check
npm run type-check
```

---

## 📦 DEPLOYMENT CHECKLIST

### **Pre-deployment**
- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] SSL certificates installed
- [ ] CORS properly configured
- [ ] File upload limits set
- [ ] Error logging configured

### **Azure Deployment**
- [ ] Azure App Service created
- [ ] MySQL database provisioned
- [ ] Static Web App configured
- [ ] CI/CD pipeline setup
- [ ] Domain configured
- [ ] Monitoring enabled
