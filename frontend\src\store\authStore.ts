import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { AuthState, LoginCredentials, RegisterData, User } from '../types/auth.types'

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true })
        try {
          // TODO: Replace with actual API call
          // Simulate API call for demo
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // Mock user data for demo
          const mockUser: User = {
            id: '1',
            email: credentials.email,
            firstName: 'John',
            lastName: 'Doe',
            role: {
              id: '1',
              name: 'super_admin',
              displayName: 'Super Admin',
              level: 5,
              permissions: ['*']
            },
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
          
          const mockToken = 'mock-jwt-token'
          
          set({
            user: mockUser,
            token: mockToken,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      register: async (data: RegisterData) => {
        set({ isLoading: true })
        try {
          // TODO: Replace with actual API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // Mock registration success
          const mockUser: User = {
            id: '2',
            email: data.email,
            firstName: data.firstName,
            lastName: data.lastName,
            role: {
              id: '2',
              name: 'project_admin',
              displayName: 'Project Admin',
              level: 4,
              permissions: ['projects.*', 'users.read']
            },
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
          
          const mockToken = 'mock-jwt-token'
          
          set({
            user: mockUser,
            token: mockToken,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
      },

      setUser: (user: User) => {
        set({ user })
      },

      setToken: (token: string) => {
        set({ token })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
