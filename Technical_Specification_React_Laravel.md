# PROJECT MANAGEMENT PLATFORM - TECHNICAL SPECIFICATION
## React Frontend + Laravel Backend

---

## 📊 PROJECT OVERVIEW

| **Field** | **Details** |
|-----------|-------------|
| **Project Name** | Project Management Platform with RBAC |
| **Frontend** | React 18 + TypeScript + Vite |
| **Backend** | Laravel 10 + PHP 8.2 |
| **Database** | MySQL 8.0 |
| **Authentication** | Laravel Sanctum + JWT |
| **Deployment** | Azure Cloud |
| **Development Time** | 6-8 weeks |

---

## 🏗️ SYSTEM ARCHITECTURE

### **Frontend Stack (React)**
| **Component** | **Technology** | **Purpose** |
|---------------|----------------|-------------|
| **Framework** | React 18 + TypeScript | Main UI framework |
| **Build Tool** | Vite | Fast development & build |
| **Routing** | React Router v6 | Client-side routing |
| **State Management** | Zustand / Redux Toolkit | Global state |
| **UI Library** | Material-UI / Ant Design | Component library |
| **HTTP Client** | Axios | API communication |
| **Forms** | React Hook Form + Zod | Form handling & validation |
| **Styling** | Tailwind CSS | Utility-first CSS |

### **Backend Stack (Laravel)**
| **Component** | **Technology** | **Purpose** |
|---------------|----------------|-------------|
| **Framework** | Laravel 10 | PHP framework |
| **Authentication** | Laravel Sanctum | API authentication |
| **Database ORM** | Eloquent | Database operations |
| **Validation** | Laravel Requests | Input validation |
| **File Storage** | Laravel Storage | File management |
| **Queue System** | Laravel Queues | Background jobs |
| **API** | Laravel API Resources | JSON API responses |
| **Testing** | PHPUnit + Pest | Unit & feature tests |

---

## 🗄️ DATABASE SCHEMA

### **Core Tables**

| **Table** | **Purpose** | **Key Fields** |
|-----------|-------------|----------------|
| `users` | User accounts | id, email, password, role_id, status |
| `roles` | User roles | id, name, permissions |
| `projects` | Project management | id, name, description, status, created_by |
| `project_users` | Project assignments | project_id, user_id, role |
| `plans` | Architectural plans | id, project_id, file_path, version |
| `overlays` | Plan overlays | id, plan_id, type, data |
| `fire_penetrations` | Fire penetration register | id, project_id, location, status |
| `defects` | Defects tracking | id, project_id, description, severity |

### **User Roles & Permissions**

| **Role** | **Level** | **Key Permissions** |
|----------|-----------|-------------------|
| **Super Admin** | 5 | Full system access, user management |
| **Project Admin** | 4 | Project creation, team management |
| **Service Provider** | 3 | Project execution, defect reporting |
| **Inspector** | 2 | Inspection, approval workflows |
| **Guest** | 1 | Read-only access to assigned projects |

---

## 🔧 DEVELOPMENT PHASES

### **Phase 1: Foundation Setup (Week 1-2)**

| **Task** | **Frontend (React)** | **Backend (Laravel)** | **Duration** |
|----------|---------------------|----------------------|--------------|
| **Environment** | Vite + React setup | Laravel installation | 1 day |
| **Database** | - | MySQL schema creation | 2 days |
| **Authentication** | Login/Register UI | Sanctum + JWT setup | 3 days |
| **RBAC** | Role-based routing | Permissions middleware | 2 days |
| **Basic Admin** | User management UI | User CRUD APIs | 2 days |

### **Phase 2: Core Features (Week 3-4)**

| **Task** | **Frontend (React)** | **Backend (Laravel)** | **Duration** |
|----------|---------------------|----------------------|--------------|
| **Projects** | Project dashboard | Project CRUD APIs | 3 days |
| **User Management** | Admin panel UI | User assignment APIs | 2 days |
| **File Upload** | Drag & drop UI | File storage system | 2 days |
| **Plans System** | Plan viewer component | Plan management APIs | 3 days |

### **Phase 3: Advanced Features (Week 5-6)**

| **Task** | **Frontend (React)** | **Backend (Laravel)** | **Duration** |
|----------|---------------------|----------------------|--------------|
| **Overlays** | Interactive plan overlays | Overlay data APIs | 4 days |
| **Fire Penetration** | Register management UI | Fire penetration APIs | 3 days |
| **Defects** | Defect tracking UI | Defect management APIs | 3 days |

### **Phase 4: Testing & Deployment (Week 7)**

| **Task** | **Frontend (React)** | **Backend (Laravel)** | **Duration** |
|----------|---------------------|----------------------|--------------|
| **Testing** | Jest + React Testing Library | PHPUnit + Feature tests | 3 days |
| **Deployment** | Azure Static Web Apps | Azure App Service | 2 days |
| **Documentation** | User guides | API documentation | 2 days |

---

## 📁 PROJECT STRUCTURE

### **Frontend Structure (React)**
```
frontend/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── common/         # Generic components
│   │   ├── forms/          # Form components
│   │   └── layout/         # Layout components
│   ├── pages/              # Page components
│   │   ├── auth/           # Login, register
│   │   ├── admin/          # Admin panel
│   │   ├── projects/       # Project management
│   │   └── dashboard/      # User dashboards
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API services
│   ├── store/              # State management
│   ├── types/              # TypeScript types
│   └── utils/              # Utility functions
├── public/                 # Static assets
└── tests/                  # Test files
```

### **Backend Structure (Laravel)**
```
backend/
├── app/
│   ├── Http/
│   │   ├── Controllers/    # API controllers
│   │   ├── Middleware/     # Custom middleware
│   │   └── Requests/       # Form requests
│   ├── Models/             # Eloquent models
│   ├── Services/           # Business logic
│   └── Policies/           # Authorization policies
├── database/
│   ├── migrations/         # Database migrations
│   ├── seeders/           # Database seeders
│   └── factories/         # Model factories
├── routes/                 # API routes
├── storage/               # File storage
└── tests/                 # Test files
```

---

## 🔐 SECURITY IMPLEMENTATION

| **Security Layer** | **Implementation** | **Technology** |
|-------------------|-------------------|----------------|
| **Authentication** | JWT tokens with refresh | Laravel Sanctum |
| **Authorization** | Role-based permissions | Laravel Policies |
| **Input Validation** | Server-side validation | Laravel Requests |
| **File Upload** | Type & size validation | Laravel Storage |
| **CORS** | Cross-origin configuration | Laravel CORS |
| **Rate Limiting** | API rate limiting | Laravel Throttle |

---

## 🚀 DEPLOYMENT STRATEGY

### **Azure Deployment Plan**

| **Component** | **Azure Service** | **Configuration** |
|---------------|------------------|-------------------|
| **Frontend** | Azure Static Web Apps | React build deployment |
| **Backend** | Azure App Service | Laravel PHP runtime |
| **Database** | Azure Database for MySQL | Managed MySQL instance |
| **Storage** | Azure Blob Storage | File uploads & plans |
| **CI/CD** | Azure DevOps | Automated deployment |

---

## 📋 ACCEPTANCE CRITERIA CHECKLIST

### **Phase 1 Deliverables**
- [ ] Azure environment setup
- [ ] MySQL database deployed
- [ ] User authentication working
- [ ] Role-based access implemented
- [ ] Basic admin panel functional

### **Phase 2 Deliverables**
- [ ] Project creation & management
- [ ] User assignment to projects
- [ ] File upload system working
- [ ] Plan viewing functionality

### **Phase 3 Deliverables**
- [ ] Interactive plan overlays
- [ ] Fire penetration register
- [ ] Defects management system
- [ ] All user roles functional

### **Final Deliverables**
- [ ] Complete system testing
- [ ] Azure deployment successful
- [ ] Documentation provided
- [ ] Demo walkthrough completed
