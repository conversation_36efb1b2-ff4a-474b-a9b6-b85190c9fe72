import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Chip,
} from '@mui/material'
import {
  Dashboard,
  FolderOpen,
  People,
  Settings,
  Assessment,
  BugReport,
  LocalFireDepartment,
  Map,
  AdminPanelSettings,
} from '@mui/icons-material'
import { useAuthStore } from '../../store/authStore'

interface NavigationItem {
  id: string
  label: string
  icon: React.ReactNode
  path: string
  badge?: string
  requiredPermissions?: string[]
  children?: NavigationItem[]
}

const Sidebar: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { user } = useAuthStore()

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Dashboard />,
      path: '/dashboard',
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: <FolderOpen />,
      path: '/projects',
      badge: '12',
    },
    {
      id: 'plans',
      label: 'Plans & Overlays',
      icon: <Map />,
      path: '/plans',
    },
    {
      id: 'fire-penetration',
      label: 'Fire Penetration',
      icon: <LocalFireDepartment />,
      path: '/fire-penetration',
    },
    {
      id: 'defects',
      label: 'Defects',
      icon: <BugReport />,
      path: '/defects',
      badge: '3',
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <Assessment />,
      path: '/reports',
    },
  ]

  const adminItems: NavigationItem[] = [
    {
      id: 'user-management',
      label: 'User Management',
      icon: <People />,
      path: '/admin/users',
      requiredPermissions: ['users.*', 'admin.*'],
    },
    {
      id: 'system-settings',
      label: 'System Settings',
      icon: <Settings />,
      path: '/admin/settings',
      requiredPermissions: ['admin.*'],
    },
  ]

  const hasPermission = (requiredPermissions?: string[]) => {
    if (!requiredPermissions || !user?.role?.permissions) return true
    
    // Super admin has all permissions
    if (user.role.permissions.includes('*')) return true
    
    return requiredPermissions.some(permission =>
      user.role.permissions.some(userPermission =>
        userPermission === permission || userPermission.endsWith('.*')
      )
    )
  }

  const handleNavigation = (path: string) => {
    navigate(path)
  }

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/')
  }

  const renderNavigationItem = (item: NavigationItem) => {
    if (!hasPermission(item.requiredPermissions)) return null

    return (
      <ListItem key={item.id} disablePadding>
        <ListItemButton
          onClick={() => handleNavigation(item.path)}
          sx={{
            borderRadius: 2,
            mx: 1,
            mb: 0.5,
            bgcolor: isActive(item.path) ? 'primary.main' : 'transparent',
            color: isActive(item.path) ? 'primary.contrastText' : 'text.primary',
            '&:hover': {
              bgcolor: isActive(item.path) ? 'primary.dark' : 'action.hover',
            },
            '& .MuiListItemIcon-root': {
              color: isActive(item.path) ? 'primary.contrastText' : 'text.secondary',
            },
          }}
        >
          <ListItemIcon sx={{ minWidth: 40 }}>
            {item.icon}
          </ListItemIcon>
          <ListItemText 
            primary={item.label}
            primaryTypographyProps={{
              fontSize: '0.875rem',
              fontWeight: isActive(item.path) ? 600 : 500,
            }}
          />
          {item.badge && (
            <Chip
              label={item.badge}
              size="small"
              sx={{
                height: 20,
                fontSize: '0.75rem',
                bgcolor: isActive(item.path) ? 'rgba(255,255,255,0.2)' : 'primary.main',
                color: isActive(item.path) ? 'primary.contrastText' : 'primary.contrastText',
              }}
            />
          )}
        </ListItemButton>
      </ListItem>
    )
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand */}
      <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              bgcolor: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <AdminPanelSettings sx={{ color: 'primary.contrastText', fontSize: 24 }} />
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 700, fontSize: '1.125rem' }}>
              ProjectHub
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Management Platform
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* User Role Badge */}
      <Box sx={{ p: 2 }}>
        <Chip
          label={user?.role?.displayName || 'User'}
          size="small"
          variant="outlined"
          sx={{
            borderColor: 'primary.main',
            color: 'primary.main',
            fontWeight: 500,
          }}
        />
      </Box>

      {/* Main Navigation */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ px: 1 }}>
          {navigationItems.map(renderNavigationItem)}
        </List>

        {/* Admin Section */}
        {adminItems.some(item => hasPermission(item.requiredPermissions)) && (
          <>
            <Divider sx={{ mx: 2, my: 2 }} />
            <Typography
              variant="overline"
              sx={{
                px: 2,
                color: 'text.secondary',
                fontWeight: 600,
                fontSize: '0.75rem',
              }}
            >
              Administration
            </Typography>
            <List sx={{ px: 1 }}>
              {adminItems.map(renderNavigationItem)}
            </List>
          </>
        )}
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Version 1.0.0
        </Typography>
      </Box>
    </Box>
  )
}

export default Sidebar
